import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/app_introduction_data.dart';
import '../../../shared/utils/user_preferences.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../themes/app_theme.dart';

/// 應用程式介紹頁面
/// 在用戶首次啟動應用時顯示，介紹應用功能和特色
class AppIntroductionPage extends StatefulWidget {
  const AppIntroductionPage({super.key});

  @override
  State<AppIntroductionPage> createState() => _AppIntroductionPageState();
}

class _AppIntroductionPageState extends State<AppIntroductionPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  int _currentPage = 0;
  final List<IntroductionPageData> _pages = AppIntroductionConfig.getIntroductionPages();

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// 跳到下一頁
  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeIntroduction();
    }
  }

  /// 跳到上一頁
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 跳過介紹
  void _skipIntroduction() {
    _completeIntroduction();
  }

  /// 完成介紹流程
  Future<void> _completeIntroduction() async {
    try {
      // 標記首次啟動流程已完成
      await UserPreferences.markFirstTimeUserCompleted();
      
      logger.i('用戶完成應用介紹流程');
      
      if (mounted) {
        // 導航到模式選擇頁面
        Navigator.pushReplacementNamed(context, '/mode-selection');
      }
    } catch (e) {
      logger.e('完成介紹流程失敗: $e');
      // 即使失敗也要導航，避免用戶卡住
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/mode-selection');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
        ),
        child: SafeArea(
          child: ResponsivePageWrapper(
            maxWidth: 600.0,
            child: Column(
              children: [
                // 頂部導航區域
                _buildTopNavigation(),
                
                // 主要內容區域
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                      // 重新播放動畫
                      _animationController.reset();
                      _animationController.forward();
                    },
                    itemCount: _pages.length,
                    itemBuilder: (context, index) {
                      return _buildIntroductionPage(_pages[index]);
                    },
                  ),
                ),
                
                // 底部導航區域
                _buildBottomNavigation(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建觸控優化按鈕包裝器
  Widget _buildTouchOptimizedButton({
    required Widget child,
    required VoidCallback onPressed,
    required String debugName,
  }) {
    return GestureDetector(
      onTap: () {
        logger.d('$debugName tapped via GestureDetector');
        onPressed();
      },
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }

  /// 構建頂部導航
  Widget _buildTopNavigation() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 返回按鈕（第一頁時隱藏）
          SizedBox(
            width: 80,
            child: _currentPage > 0
                ? _buildTouchOptimizedButton(
                    onPressed: _previousPage,
                    debugName: 'Previous button',
                    child: TextButton(
                      onPressed: () {
                        logger.d('Previous button pressed via onPressed');
                        _previousPage();
                      },
                      child: const Text(
                        '上一步',
                        style: TextStyle(
                          color: AppColors.royalIndigo,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  )
                : null,
          ),

          // 頁面指示器
          _buildPageIndicator(),

          // 跳過按鈕（最後一頁時隱藏）
          SizedBox(
            width: 60,
            child: _currentPage < _pages.length - 1
                ? _buildTouchOptimizedButton(
                    onPressed: _skipIntroduction,
                    debugName: 'Skip button',
                    child: TextButton(
                      onPressed: () {
                        logger.d('Skip button pressed via onPressed');
                        _skipIntroduction();
                      },
                      child: const Text(
                        '跳過',
                        style: TextStyle(
                          color: AppColors.textMedium,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  )
                : null,
          ),
        ],
      ),
    );
  }

  /// 構建頁面指示器
  Widget _buildPageIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(_pages.length, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentPage == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? _pages[_currentPage].primaryColor
                : AppColors.textLight,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }

  /// 構建介紹頁面內容
  Widget _buildIntroductionPage(IntroductionPageData pageData) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Column(
            children: [
              // 圖標區域
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: pageData.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(
                  pageData.icon,
                  size: 50,
                  color: pageData.primaryColor,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 標題
              Text(
                pageData.title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: pageData.primaryColor,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // 副標題
              Text(
                pageData.subtitle,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textDark,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 描述
              Text(
                pageData.description,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textMedium,
                  height: 1.5,
                ),
              ),
              
              const SizedBox(height: 36),
              
              // 功能列表
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: pageData.features.map((feature) {
                      return _buildFeatureCard(feature);
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建功能卡片
  Widget _buildFeatureCard(AppFeature feature) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 功能圖標
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: feature.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              feature.icon,
              size: 24,
              color: feature.color,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 功能內容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  feature.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textMedium,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建底部導航
  Widget _buildBottomNavigation() {
    final isLastPage = _currentPage == _pages.length - 1;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      child: Column(
        children: [

          // 主要按鈕
          SizedBox(
            width: double.infinity,
            height: 50,
            child: _buildTouchOptimizedButton(
              onPressed: isLastPage ? _completeIntroduction : _nextPage,
              debugName: isLastPage ? 'Complete button' : 'Next button',
              child: ElevatedButton(
                onPressed: () {
                  logger.d('${isLastPage ? 'Complete' : 'Next'} button pressed via onPressed');
                  if (isLastPage) {
                    _completeIntroduction();
                  } else {
                    _nextPage();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _pages[_currentPage].primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  isLastPage ? '開始使用' : '下一步',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
